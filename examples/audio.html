<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kids Keyboard - Audio Testing</title>
    <link rel="stylesheet" href="../src/kids-keyboard.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            min-height: 100vh;
            padding: 20px;
        }

        #app-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 0 auto;
            max-width: 1000px;
            padding: 30px;
        }

        h1 {
            color: #4a5568;
            margin-bottom: 30px;
            text-align: center;
        }

        .audio-controls {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 20px;
        }

        .audio-controls h3 {
            color: #2d3748;
            margin-bottom: 15px;
            margin-top: 0;
        }

        .control-group {
            align-items: center;
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .control-group label {
            font-weight: 500;
            min-width: 100px;
        }

        .toggle-button {
            background: #e2e8f0;
            border: 2px solid #cbd5e0;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            padding: 8px 16px;
            transition: all 0.2s ease;
        }

        .toggle-button.active {
            background: #4CAF50;
            border-color: #45a049;
            color: white;
        }

        .slider {
            flex: 1;
            max-width: 200px;
        }

        .status-indicator {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            margin-bottom: 20px;
            padding: 15px;
        }

        .status-indicator.ready {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .status-indicator.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
    </style>
</head>

<body>
    <div id="app-container">
        <h1>🎵 Kids Keyboard - Audio Testing</h1>

        <div id="audio-status" class="status-indicator">
            🔄 Initializing audio system...
        </div>

        <div class="audio-controls">
            <h3>🔊 Audio Settings</h3>

            <div class="control-group">
                <label>Audio:</label>
                <button id="audio-toggle" class="toggle-button active">🔊 ON</button>
            </div>

            <div class="control-group">
                <label>Speed:</label>
                <input type="range" id="speech-rate" class="slider" min="0.5" max="2" step="0.1" value="0.8">
                <span id="rate-value">0.8x</span>
            </div>

            <div class="control-group">
                <label>Pitch:</label>
                <input type="range" id="speech-pitch" class="slider" min="0.5" max="2" step="0.1" value="1.1">
                <span id="pitch-value">1.1x</span>
            </div>
        </div>

        <div id="kids-keyboard-tutor">
            <div id="kids-keyboard-output">
                <textarea id="kids-keyboard-text"
                          placeholder="Click virtual keys to test audio feedback!"></textarea>
                <div id="kids-keyboard-display">
                    <div style="text-align: center; color: #666; padding: 20px;">
                        🎯 Click any key to hear audio feedback!
                        <br><br>
                        <small>Phase 1A: Basic Audio System</small>
                    </div>
                </div>
            </div>
            <div id="kids-keyboard-input"></div>
        </div>
    </div>

    <script src="../src/kids-keyboard.js"></script>
    <script>
        // Audio testing will be implemented here in Phase 1A
        console.log('🎵 Audio testing page loaded - ready for Phase 1A implementation!');

        // Initialize basic keyboard for testing
        const keyboard = createKidsKeyboard({
            container: '#kids-keyboard-input',
            targetOutput: '#kids-keyboard-text',
            tutorContainer: '#kids-keyboard-tutor',
            debug: true,

            onKeyPress: (key) => {
                console.log('🎹 Key pressed:', key);
                // Audio implementation will go here
            },

            onTutorModeChange: (isActive) => {
                console.log('🎯 Tutor mode:', isActive ? 'ON' : 'OFF');
            }
        });

        // Update status
        document.getElementById('audio-status').innerHTML = '✅ Ready for audio implementation!';
        document.getElementById('audio-status').className = 'status-indicator ready';
    </script>
</body>
</html>