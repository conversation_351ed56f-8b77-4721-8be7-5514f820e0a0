<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kids Keyboard - Audio Testing</title>
    <link rel="stylesheet" href="../src/kids-keyboard.css">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            min-height: 100vh;
            padding: 20px;
        }

        #app-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            margin: 0 auto;
            max-width: 1000px;
            padding: 30px;
        }

        h1 {
            color: #4a5568;
            margin-bottom: 30px;
            text-align: center;
        }

        .audio-controls {
            background: #f8f9fa;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
            margin-bottom: 20px;
            padding: 20px;
        }

        .audio-controls h3 {
            color: #2d3748;
            margin-bottom: 15px;
            margin-top: 0;
        }

        .control-group {
            align-items: center;
            display: flex;
            gap: 10px;
            margin-bottom: 10px;
        }

        .control-group label {
            font-weight: 500;
            min-width: 100px;
        }

        .toggle-button {
            background: #e2e8f0;
            border: 2px solid #cbd5e0;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
            padding: 8px 16px;
            transition: all 0.2s ease;
        }

        .toggle-button.active {
            background: #4CAF50;
            border-color: #45a049;
            color: white;
        }

        .slider {
            flex: 1;
            max-width: 200px;
        }

        .status-indicator {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            margin-bottom: 20px;
            padding: 15px;
        }

        .status-indicator.ready {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }

        .status-indicator.error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }

        .audio-feedback {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            border-radius: 6px;
            color: #1565c0;
            font-size: 14px;
            margin-top: 10px;
            min-height: 20px;
            padding: 10px;
            transition: all 0.3s ease;
        }

        .audio-feedback.active {
            background: #c8e6c9;
            border-color: #a5d6a7;
            color: #2e7d32;
        }
    </style>
</head>

<body>
    <div id="app-container">
        <h1>🎵 Kids Keyboard - Audio Testing</h1>

        <div id="audio-status" class="status-indicator">
            🔄 Initializing audio system...
        </div>

        <div class="audio-controls">
            <h3>🔊 Audio Settings</h3>

            <div class="control-group">
                <label>Audio:</label>
                <button id="audio-toggle" class="toggle-button active">🔊 ON</button>
            </div>

            <div class="control-group">
                <label>Speed:</label>
                <input type="range" id="speech-rate" class="slider" min="0.5" max="2" step="0.1" value="0.8">
                <span id="rate-value">0.8x</span>
            </div>

            <div class="control-group">
                <label>Pitch:</label>
                <input type="range" id="speech-pitch" class="slider" min="0.5" max="2" step="0.1" value="1.1">
                <span id="pitch-value">1.1x</span>
            </div>

            <div id="audio-feedback" class="audio-feedback">
                💡 Try typing quickly to test the improved audio debouncing!
            </div>
        </div>

        <div id="kids-keyboard-tutor">
            <div id="kids-keyboard-output">
                <textarea id="kids-keyboard-text"
                          placeholder="Click virtual keys to test audio feedback!"></textarea>
                <div id="kids-keyboard-display">
                    <div style="text-align: center; color: #666; padding: 20px;">
                        🎯 Click any key to hear audio feedback!
                        <br><br>
                        <small>Phase 1A: Basic Audio System</small>
                    </div>
                </div>
            </div>
            <div id="kids-keyboard-input"></div>
        </div>
    </div>

    <script src="../src/kids-keyboard.js"></script>
    <script src="../src/features/kids-keyboard-audio-webAPI.js"></script>
    <script>
        console.log('🎵 Phase 1A: Functional Audio Testing Implementation');

        let audioSystem = null;

        // Initialize audio system (async)
        const initializeAudio = async () => {
            try {
                audioSystem = await createKidsKeyboardAudio({
                    enabled: true,
                    rate: 0.8,
                    pitch: 1.1
                });

                console.log('🎵 Audio system initialized:', audioSystem.getStatus());
                updateAudioStatus();
                setupAudioControls();

            } catch (error) {
                console.error('🔇 Failed to initialize audio:', error);
                updateAudioStatus(false);
            }
        };

        // Initialize keyboard with audio integration
        const keyboard = createKidsKeyboard({
            container: '#kids-keyboard-input',
            targetOutput: '#kids-keyboard-text',
            tutorContainer: '#kids-keyboard-tutor',
            debug: true,

            onKeyPress: (key, event, inputSource) => {
                console.log('🎹 Key pressed:', key, 'from', inputSource);

                // 🎵 PHASE 1A: Different audio for different input sources
                if (audioSystem) {
                    if (inputSource === 'physical') {
                        // Physical keyboard: fast, simple audio
                        audioSystem.playPhysicalKeySound(key);
                    } else if (inputSource === 'virtual') {
                        // Virtual keyboard: detailed, educational audio
                        audioSystem.playVirtualKeySound(key);
                    } else {
                        // Fallback for unknown source
                        audioSystem.playKeySound(key);
                    }
                }

                // Update display with key information
                updateKeyDisplay(key, inputSource);
            },

            onTutorModeChange: (isActive) => {
                console.log('🎯 Tutor mode:', isActive ? 'ON' : 'OFF');
            }
        });

        // Initialize audio system
        initializeAudio();

        // Setup audio controls (called after audio system is initialized)
        const setupAudioControls = () => {
            const audioToggle = document.getElementById('audio-toggle');
            const speechRate = document.getElementById('speech-rate');
            const speechPitch = document.getElementById('speech-pitch');
            const rateValue = document.getElementById('rate-value');
            const pitchValue = document.getElementById('pitch-value');

            // Audio toggle
            audioToggle.addEventListener('click', () => {
                if (!audioSystem) return;
                const enabled = audioSystem.toggle();
                audioToggle.textContent = enabled ? '🔊 ON' : '🔇 OFF';
                audioToggle.className = `toggle-button ${enabled ? 'active' : ''}`;
            });

            // Speech rate control
            speechRate.addEventListener('input', (e) => {
                if (!audioSystem) return;
                const rate = parseFloat(e.target.value);
                audioSystem.setRate(rate);
                rateValue.textContent = rate + 'x';
            });

            // Speech pitch control
            speechPitch.addEventListener('input', (e) => {
                if (!audioSystem) return;
                const pitch = parseFloat(e.target.value);
                audioSystem.setPitch(pitch);
                pitchValue.textContent = pitch + 'x';
            });

            // Set initial values from config
            const config = audioSystem.getConfig();
            speechRate.value = config.rate;
            speechPitch.value = config.pitch;
            rateValue.textContent = config.rate + 'x';
            pitchValue.textContent = config.pitch + 'x';
            audioToggle.textContent = config.enabled ? '🔊 ON' : '🔇 OFF';
            audioToggle.className = `toggle-button ${config.enabled ? 'active' : ''}`;
        };

        // Update key display
        function updateKeyDisplay(key, inputSource = 'unknown') {
            const display = document.getElementById('kids-keyboard-display');
            const feedback = document.getElementById('audio-feedback');
            const keyUpper = key.toUpperCase();

            // Different display based on input source
            let content = `<div style="text-align: center;">`;

            // Input source indicator
            const sourceIcon = inputSource === 'physical' ? '⌨️' : inputSource === 'virtual' ? '🖱️' : '❓';
            const sourceText = inputSource === 'physical' ? 'Physical Keyboard' : inputSource === 'virtual' ? 'Virtual Keyboard' : 'Unknown';
            content += `<div style="font-size: 12px; color: #666; margin-bottom: 10px;">${sourceIcon} ${sourceText}</div>`;

            content += `<div style="font-size: 48px; margin: 20px 0; color: #4CAF50;">${keyUpper}</div>`;

            if (key.length === 1 && /[a-z]/i.test(key)) {
                content += `<div style="font-size: 18px; margin: 10px 0;">Letter: <strong>${keyUpper}</strong></div>`;

                if (inputSource === 'physical') {
                    content += `<div style="font-size: 16px; color: #666;">🔊 Quick: "${keyUpper}"</div>`;
                    feedback.textContent = `⌨️ Physical: "${keyUpper}"`;
                } else if (inputSource === 'virtual') {
                    content += `<div style="font-size: 16px; color: #666;">🔊 Educational: "Letter ${keyUpper}. ${keyUpper} says ${getPhoneticSound(key)}"</div>`;
                    content += `<div style="font-size: 14px; color: #888; margin-top: 10px;">🍎 Future: Picture of ${getAssociation(key)}</div>`;
                    feedback.textContent = `🖱️ Virtual: "Letter ${keyUpper}. ${keyUpper} says ${getPhoneticSound(key)}"`;
                } else {
                    content += `<div style="font-size: 16px; color: #666;">🔊 "${keyUpper} says ${getPhoneticSound(key)}"</div>`;
                    feedback.textContent = `🎵 Playing: "${keyUpper} says ${getPhoneticSound(key)}"`;
                }
            } else if (key.length === 1 && /[0-9]/.test(key)) {
                content += `<div style="font-size: 18px; margin: 10px 0;">Number: <strong>${key}</strong></div>`;

                if (inputSource === 'physical') {
                    content += `<div style="font-size: 16px; color: #666;">🔊 Quick: "${key}"</div>`;
                    feedback.textContent = `⌨️ Physical: "${key}"`;
                } else if (inputSource === 'virtual') {
                    content += `<div style="font-size: 16px; color: #666;">🔊 Educational: "Number ${key}. ${key} is ${getNumberWord(key)}"</div>`;
                    feedback.textContent = `🖱️ Virtual: "Number ${key}. ${key} is ${getNumberWord(key)}"`;
                } else {
                    content += `<div style="font-size: 16px; color: #666;">🔊 "${key} is ${getNumberWord(key)}"</div>`;
                    feedback.textContent = `🎵 Playing: "${key} is ${getNumberWord(key)}"`;
                }
            } else {
                content += `<div style="font-size: 18px; margin: 10px 0;">Key: <strong>${keyUpper}</strong></div>`;
                content += `<div style="font-size: 16px; color: #666;">🔊 Function key</div>`;
                feedback.textContent = `🎵 Playing: ${keyUpper} function key`;
            }

            content += `</div>`;
            display.innerHTML = content;

            // Show active feedback
            feedback.className = 'audio-feedback active';

            // Reset feedback after a delay
            setTimeout(() => {
                feedback.className = 'audio-feedback';
                feedback.textContent = '💡 Try both physical keyboard and mouse clicks to hear the difference!';
            }, 3000);
        }

        // Helper functions
        function getPhoneticSound(letter) {
            const sounds = {
                'a': 'ah', 'b': 'buh', 'c': 'kuh', 'd': 'duh', 'e': 'eh',
                'f': 'fuh', 'g': 'guh', 'h': 'huh', 'i': 'ih', 'j': 'juh',
                'k': 'kuh', 'l': 'luh', 'm': 'muh', 'n': 'nuh', 'o': 'oh',
                'p': 'puh', 'q': 'kwuh', 'r': 'ruh', 's': 'suh', 't': 'tuh',
                'u': 'uh', 'v': 'vuh', 'w': 'wuh', 'x': 'ksuh', 'y': 'yuh', 'z': 'zuh'
            };
            return sounds[letter.toLowerCase()] || letter;
        }

        function getNumberWord(number) {
            const words = {
                '0': 'zero', '1': 'one', '2': 'two', '3': 'three', '4': 'four',
                '5': 'five', '6': 'six', '7': 'seven', '8': 'eight', '9': 'nine'
            };
            return words[number] || number;
        }

        function getAssociation(letter) {
            const associations = {
                'a': 'Apple', 'b': 'Bear', 'c': 'Cat', 'd': 'Dog', 'e': 'Elephant',
                'f': 'Fish', 'g': 'Giraffe', 'h': 'Horse', 'i': 'Ice cream', 'j': 'Jellyfish',
                'k': 'Kite', 'l': 'Lion', 'm': 'Mouse', 'n': 'Nest', 'o': 'Orange',
                'p': 'Penguin', 'q': 'Queen', 'r': 'Rabbit', 's': 'Sun', 't': 'Tiger',
                'u': 'Umbrella', 'v': 'Violin', 'w': 'Whale', 'x': 'X-ray', 'y': 'Yo-yo', 'z': 'Zebra'
            };
            return associations[letter.toLowerCase()] || letter;
        }

        // Update audio status display
        const updateAudioStatus = (success = true) => {
            const statusEl = document.getElementById('audio-status');

            if (!success || !audioSystem) {
                statusEl.innerHTML = '❌ Web Speech API not supported in this browser';
                statusEl.className = 'status-indicator error';
                return;
            }

            const status = audioSystem.getStatus();

            if (status.supported && status.initialized) {
                statusEl.innerHTML = `✅ Audio system ready! ${status.voicesAvailable} voices available. Selected: ${status.selectedVoice}`;
                statusEl.className = 'status-indicator ready';
            } else if (status.supported) {
                statusEl.innerHTML = '🔄 Loading voices...';
                statusEl.className = 'status-indicator';
            } else {
                statusEl.innerHTML = '❌ Web Speech API not supported in this browser';
                statusEl.className = 'status-indicator error';
            }
        };

        // Test button (for debugging)
        console.log('🎯 After audio loads, type audioSystem.test() in console to test audio');
    </script>
</body>
</html>