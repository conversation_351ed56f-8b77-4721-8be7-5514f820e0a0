/**
 * Kids Keyboard - Stats Example Styles
 * 
 * Specific styles for the stats.html example page.
 * This file contains UI elements unique to the statistics demo.
 * 
 * @version 0.9.0
 * <AUTHOR>
 * @license MIT
 */

/* =============================================================================
   PAGE LAYOUT
   ============================================================================= */

body {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  margin: 0;
  min-height: 100vh;
  padding: 20px;
}

#app-container {
  background: white;
  border-radius: 16px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  margin: 0 auto;
  max-width: 1000px;
  padding: 30px;
}

h1 {
  color: #4a5568;
  margin-bottom: 30px;
  text-align: center;
}

/* =============================================================================
   STATISTICS DISPLAY
   ============================================================================= */

#kids-keyboard-stats {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  margin-bottom: 20px;
}

.kids-keyboard-stat-item {
  background: #f8f9fa;
  border: 1px solid #e0e0e0;
  border-radius: 6px;
  flex: 1;
  min-width: 120px;
  padding: 12px 16px;
}

.kids-keyboard-stat-item__label {
  color: #666;
  font-size: 12px;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
  text-transform: uppercase;
}

.kids-keyboard-stat-item__value {
  color: #2c3e50;
  font-size: 18px;
  font-weight: 600;
}

/* =============================================================================
   RESPONSIVE DESIGN FOR STATS
   ============================================================================= */

@media (max-width: 768px) {
  #app-container {
    margin: 10px;
    padding: 20px;
  }

  #kids-keyboard-stats {
    flex-direction: column;
    gap: 10px;
  }

  .kids-keyboard-stat-item {
    min-width: auto;
  }

  h1 {
    font-size: 1.5em;
    margin-bottom: 20px;
  }
}
