<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kids Keyboard Example from SRC</title>
    <link rel="stylesheet" href="../src/kids-keyboard.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        #app-container {
            max-width: 1000px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        h1 {
            text-align: center;
            color: #2c3e50;
            margin-bottom: 30px;
        }
        
        #tutor-input {
            width: calc(100% - 40px);
            min-height: 120px;
            padding: 15px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            font-size: 16px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            background: #f8f9fa;
            resize: vertical;
            line-height: 1.5;
            margin-bottom: 20px;
        }
        
        #tutor-input:focus {
            outline: none;
            border-color: #4CAF50;
            box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
        }
        
        #tutor-stats {
            display: flex;
            gap: 20px;
            margin-bottom: 20px;
            flex-wrap: wrap;
        }
        
        .tutor-stat-item {
            background: #f8f9fa;
            padding: 12px 16px;
            border-radius: 6px;
            border: 1px solid #e0e0e0;
            flex: 1;
            min-width: 120px;
        }
        
        .tutor-stat-label {
            font-size: 12px;
            color: #666;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            margin-bottom: 4px;
        }
        
        .tutor-stat-value {
            font-size: 18px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        #tutor-container {
            position: relative;
            transition: all 0.3s ease;
            border-radius: 12px;
            padding: 15px;
            margin: 15px 0;
            background-color: lightpink;
            border: 2px solid fuchsia;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        
        #tutor-container::before {
            content: "TUTOR MODE OFF";
            position: absolute;
            top: -18px;
            left: 50px;
            background: #9e9e9e;
            color: white;
            padding: 6px 16px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 700;
            letter-spacing: 0.5px;
            z-index: 1000;
            transition: all 0.3s ease;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        
        #tutor-container::after {
            content: "🎯";
            position: absolute;
            top: -22px;
            left: 20px;
            font-size: 20px;
            z-index: 1001;
            transition: all 0.3s ease;
        }
        
        #tutor-container.tutor-mode-active {
            background-color: rgba(76, 175, 80, 0.08);
            border: 2px solid #4CAF50;
            box-shadow: 0 4px 20px rgba(76, 175, 80, 0.25);
        }
        
        #tutor-container.tutor-mode-active::before {
            content: "TUTOR MODE ON";
            background: linear-gradient(135deg, #4CAF50, #66BB6A);
            color: white;
            box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
            transform: scale(1.05);
        }
        
        #tutor-container.tutor-mode-active::after {
            font-size: 24px;
            transform: scale(1.1) rotate(5deg);
        }
    </style>
</head>
<body>
    <div id="app-container">
        <h1>Kids Keyboard v0.9.0 - Example</h1>
        
        <div id="tutor-stats">
            <div class="tutor-stat-item">
                <div class="tutor-stat-label">Characters</div>
                <div class="tutor-stat-value" id="charCount">0</div>
            </div>
            <div class="tutor-stat-item">
                <div class="tutor-stat-label">Words</div>
                <div class="tutor-stat-value" id="wordCount">0</div>
            </div>
            <div class="tutor-stat-item">
                <div class="tutor-stat-label">Tutor Mode</div>
                <div class="tutor-stat-value" id="tutorMode">OFF</div>
            </div>
        </div>
        
        <div id="tutor-container">
            <textarea id="tutor-input" 
                      placeholder="Hover over this area to activate tutor mode, then start typing!"></textarea>
            <div id="tutor-keyboard-container"></div>
        </div>
    </div>

    <script src="../src/kids-keyboard.js"></script>
    <script>
        let keyboard;
        const tutorContainer = document.getElementById('tutor-container');

        function initKeyboard() {
            keyboard = createKidsKeyboard({
                container: '#tutor-keyboard-container',
                targetInput: '#tutor-input',
                tutorContainer: '#tutor-container',
                debug: true,
                
                onChange: (input) => {
                    console.log('Input changed:', input);
                    updateStats();
                },
                
                onKeyPress: (key) => {
                    console.log('Key pressed:', key);
                },
                
                onTutorModeChange: (isActive) => {
                    console.log('Tutor mode:', isActive ? 'ON' : 'OFF');
                    document.getElementById('tutorMode').textContent = isActive ? 'ON' : 'OFF';
                    tutorContainer.classList.toggle('tutor-mode-active', isActive);
                }
            });
        }
        
        function updateStats() {
            const text = keyboard ? keyboard.getInput() : '';
            document.getElementById('charCount').textContent = text.length;
            document.getElementById('wordCount').textContent = text.trim() ? text.trim().split(/\s+/).length : 0;
        }
        
        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function() {
            initKeyboard();
            updateStats();
            
            // Sync external typing with keyboard
            document.getElementById('tutor-input').addEventListener('input', function(e) {
                if (keyboard && !keyboard.isTutorModeActive()) {
                    keyboard.setInput(e.target.value);
                }
                updateStats();
            });
            
            console.log('✅ Kids Keyboard v0.9.0 loaded successfully!');
        });
    </script>
</body>
</html>