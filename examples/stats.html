<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kids Keyboard Example from SRC</title>
    <link rel="stylesheet" href="../src/kids-keyboard.css">
    <link rel="stylesheet" href="stats.css">

</head>

<body>
    <div id="app-container">
        <h1>Kids Keyboard v0.9.0 - Example</h1>

        <div id="kids-keyboard-stats">
            <div class="kids-keyboard-stat-item">
                <div class="kids-keyboard-stat-item__label">Characters</div>
                <div class="kids-keyboard-stat-item__value" id="kids-keyboard-char-count">0</div>
            </div>
            <div class="kids-keyboard-stat-item">
                <div class="kids-keyboard-stat-item__label">Words</div>
                <div class="kids-keyboard-stat-item__value" id="kids-keyboard-word-count">0</div>
            </div>
            <div class="kids-keyboard-stat-item">
                <div class="kids-keyboard-stat-item__label">Tutor Mode</div>
                <div class="kids-keyboard-stat-item__value" id="kids-keyboard-tutor-mode">OFF</div>
            </div>
        </div>

        <div id="kids-keyboard-tutor">
            <div id="kids-keyboard-output">
                <textarea id="kids-keyboard-text"
                    placeholder="Hover over this area to activate tutor mode, then start typing!"></textarea>
                <div id="kids-keyboard-display">
                </div>
            </div>
            <div id="kids-keyboard-input"></div>
        </div>
    </div>

    <script src="../src/kids-keyboard.js"></script>
    <script>
        let keyboard;
        const tutorContainer = document.getElementById('kids-keyboard-tutor');

        function initKeyboard() {
            keyboard = createKidsKeyboard({
                container: '#kids-keyboard-input',
                targetOutput: '#kids-keyboard-text',
                tutorContainer: '#kids-keyboard-tutor',
                debug: true,

                onChange: (input) => {
                    console.log('Input changed:', input);
                    updateStats();
                },

                onKeyPress: (key) => {
                    console.log('Key pressed:', key);
                },

                onTutorModeChange: (isActive) => {
                    console.log('Tutor mode:', isActive ? 'ON' : 'OFF');
                    document.getElementById('kids-keyboard-tutor-mode').textContent = isActive ? 'ON' : 'OFF';
                    tutorContainer.classList.toggle('active', isActive);
                }
            });
        }

        function updateStats() {
            const text = keyboard ? keyboard.getInput() : '';
            document.getElementById('kids-keyboard-char-count').textContent = text.length;
            document.getElementById('kids-keyboard-word-count').textContent = text.trim() ? text.trim().split(/\s+/).length : 0;
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', function () {
            initKeyboard();
            updateStats();

            // Sync external typing with keyboard
            document.getElementById('kids-keyboard-text').addEventListener('input', function (e) {
                if (keyboard && !keyboard.isTutorModeActive()) {
                    keyboard.setInput(e.target.value);
                }
                updateStats();
            });

            console.log('✅ Kids Keyboard v0.9.0 loaded successfully!');
        });
    </script>
</body>

</html>