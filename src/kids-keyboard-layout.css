/**
 * Kids Keyboard - Layout Styles
 * 
 * Layout containers, tutor mode, and responsive design.
 * This file contains the structural CSS for the keyboard application.
 * 
 * @version 0.9.0
 * <AUTHOR>
 * @license MIT
 */

/* =============================================================================
   TUTOR MODE CONTAINER STYLING
   ============================================================================= */

/* Base tutor container - ID because there should only be one per page */
#kids-keyboard-tutor {
  background-color: lightpink;
  border: 2px solid fuchsia;
  border-radius: 12px;
  margin: 15px 0;
  padding: 15px;
  position: relative;
  transition: all 0.3s ease;
}

/* Tutor Mode OFF state indicator */
#kids-keyboard-tutor::before {
  background: #9e9e9e;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  color: white;
  content: "TUTOR MODE OFF";
  font-size: 12px;
  font-weight: 700;
  left: 50px; /* Make room for emoji */
  letter-spacing: 0.5px;
  padding: 6px 16px;
  position: absolute;
  top: -18px;
  transition: all 0.3s ease;
  z-index: 1000;
}

/* Large emoji indicator */
#kids-keyboard-tutor::after {
  content: "🎯";
  font-size: 20px;
  left: 20px;
  position: absolute;
  top: -22px;
  transition: all 0.3s ease;
  z-index: 1001;
}

/* Tutor Mode ON state */
#kids-keyboard-tutor.active {
  background-color: rgba(76, 175, 80, 0.08);
  border: 2px solid #4CAF50;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.25);
}

#kids-keyboard-tutor.active::before {
  background: linear-gradient(135deg, #4CAF50, #66BB6A);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
  color: white;
  content: "TUTOR MODE ON";
  transform: scale(1.05);
}

#kids-keyboard-tutor.active::after {
  font-size: 24px;
  transform: scale(1.1) rotate(5deg);
}

/* Keyboard enhancement in tutor mode */
#kids-keyboard-tutor .kids-keyboard {
  transition: box-shadow 0.3s ease;
}

#kids-keyboard-tutor.active .kids-keyboard {
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.15);
}

/* =============================================================================
   OUTPUT SECTION LAYOUT
   ============================================================================= */

#kids-keyboard-output {
  display: flex;
  flex-direction: row;
  gap: 10px;
  margin-bottom: 20px;
}

#kids-keyboard-text {
  background: #f8f9fa;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  flex: 1;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 16px;
  line-height: 1.5;
  min-height: 120px;
  padding: 15px;
  resize: vertical;
  transition: all 0.3s ease;
}

#kids-keyboard-text:focus {
  background: white;
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
  outline: none;
}

#kids-keyboard-display {
  background: #f8f9fa;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  flex: 0 0 200px;
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  line-height: 1.4;
  min-height: 120px;
  overflow-y: auto;
  padding: 15px;
  transition: all 0.3s ease;
}

/* =============================================================================
   RESPONSIVE DESIGN - DESKTOP/LAPTOP/TABLET
   ============================================================================= */

/* Tablet and small desktop */
@media (max-width: 1024px) {
  .kids-keyboard {
    max-width: 90vw;
    padding: 10px;
  }
  
  .kids-keyboard__key {
    font-size: 14px;
    min-height: 40px;
    min-width: 40px;
  }
  
  .kids-keyboard__key--function,
  .kids-keyboard__key--modifier {
    font-size: 10px;
  }
}

/* Small tablets and large phones */
@media (max-width: 768px) {
  .kids-keyboard {
    max-width: 95vw;
    padding: 8px;
  }
  
  .kids-keyboard__key {
    font-size: 12px;
    min-height: 35px;
    min-width: 35px;
  }
  
  .kids-keyboard__key--function,
  .kids-keyboard__key--modifier {
    font-size: 9px;
  }
  
  .kids-keyboard__row {
    gap: 3px;
    margin-bottom: 4px;
  }

  /* Stack output vertically on mobile */
  #kids-keyboard-output {
    flex-direction: column;
  }

  #kids-keyboard-display {
    flex: 1;
    min-height: 80px;
  }
}

/* =============================================================================
   ACCESSIBILITY ENHANCEMENTS
   ============================================================================= */

/* High contrast mode support */
@media (prefers-contrast: high) {
  .kids-keyboard__key {
    border-width: 3px;
  }
  
  .kids-keyboard__key--highlight-normal {
    background-color: #2E7D32 !important;
  }
  
  .kids-keyboard__key--highlight-modifier {
    background-color: #F57F17 !important;
  }
  
  .kids-keyboard__key--highlight-function {
    background-color: #1565C0 !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .kids-keyboard__key,
  #kids-keyboard-tutor,
  #kids-keyboard-tutor::before,
  #kids-keyboard-tutor::after {
    transition: none;
  }
  
  .kids-keyboard__key:hover,
  .kids-keyboard__key:focus,
  .kids-keyboard__key--highlighted {
    transform: none;
  }
}

/* =============================================================================
   PRINT STYLES
   ============================================================================= */

@media print {
  .kids-keyboard {
    border: 1px solid #ccc;
    box-shadow: none;
  }
  
  .kids-keyboard__key {
    box-shadow: none;
  }
}
