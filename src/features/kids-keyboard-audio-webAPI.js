/**
 * Kids Keyboard - Web Speech API Audio Feature
 *
 * Phase 1A: Basic Audio System for Pre-School Children (Ages 3-5)
 * Provides phonetic pronunciation using the Web Speech API.
 *
 * @version 0.10.0
 * <AUTHOR>
 * @license MIT
 *
 * FEATURES:
 * - Letter phonetic sounds ("A says 'ah'")
 * - Audio toggle with persistent settings
 * - Kid-friendly voice selection
 * - Adjustable speech rate and pitch
 * - Fallback for unsupported browsers
 *
 * EDUCATIONAL GOALS:
 * - Letter recognition for pre-school children
 * - Phonetic awareness development
 * - Cause-and-effect learning through interaction
 */

/**
 * Phonetic sound mappings for letters
 * Based on common phonics education standards
 */
const PHONETIC_SOUNDS = {
    'a': 'ah',     'b': 'buh',    'c': 'kuh',    'd': 'duh',    'e': 'eh',
    'f': 'fuh',    'g': 'guh',    'h': 'huh',    'i': 'ih',     'j': 'juh',
    'k': 'kuh',    'l': 'luh',    'm': 'muh',    'n': 'nuh',    'o': 'oh',
    'p': 'puh',    'q': 'kwuh',   'r': 'ruh',    's': 'suh',    't': 'tuh',
    'u': 'uh',     'v': 'vuh',    'w': 'wuh',    'x': 'ksuh',   'y': 'yuh',
    'z': 'zuh'
};

/**
 * Number pronunciations for digits
 */
const NUMBER_SOUNDS = {
    '0': 'zero',   '1': 'one',    '2': 'two',    '3': 'three',  '4': 'four',
    '5': 'five',   '6': 'six',    '7': 'seven',  '8': 'eight',  '9': 'nine'
};

/**
 * Function key descriptions
 */
const FUNCTION_KEY_SOUNDS = {
    'space': 'space bar',
    'enter': 'enter key',
    'backspace': 'backspace',
    'tab': 'tab key',
    'capslock': 'caps lock',
    'shiftleft': 'left shift',
    'shiftright': 'right shift'
};

/**
 * Audio system configuration
 */
const DEFAULT_AUDIO_CONFIG = {
    enabled: true,
    rate: 0.8,        // Slower for kids to process
    pitch: 1.1,       // Slightly higher for friendliness
    volume: 0.8,      // Not too loud
    voice: null,      // Will be set to kid-friendly voice
    language: 'en-US'
};

/**
 * Kids Keyboard Audio System
 * Manages Web Speech API integration for educational audio feedback
 */
class KidsKeyboardAudio {
    constructor(options = {}) {
        this.config = { ...DEFAULT_AUDIO_CONFIG, ...options };
        this.isSupported = this.checkSupport();
        this.voices = [];
        this.selectedVoice = null;
        this.isInitialized = false;

        // Load saved settings
        this.loadSettings();

        // Initialize when voices are available
        this.initializeVoices();
    }

    /**
     * Check if Web Speech API is supported
     */
    checkSupport() {
        if (typeof window === 'undefined') return false;
        return 'speechSynthesis' in window && 'SpeechSynthesisUtterance' in window;
    }

    /**
     * Initialize voice selection
     */
    initializeVoices() {
        if (!this.isSupported) {
            console.warn('🔇 Web Speech API not supported in this browser');
            return;
        }

        // Load voices (may be async in some browsers)
        const loadVoices = () => {
            this.voices = speechSynthesis.getVoices();
            if (this.voices.length > 0) {
                this.selectKidFriendlyVoice();
                this.isInitialized = true;
                console.log('🎵 Audio system initialized with', this.voices.length, 'voices');
            }
        };

        // Try loading immediately
        loadVoices();

        // Also listen for the voiceschanged event (Chrome, Safari)
        if (speechSynthesis.onvoiceschanged !== undefined) {
            speechSynthesis.onvoiceschanged = loadVoices;
        }
    }

    /**
     * Select a kid-friendly voice
     */
    selectKidFriendlyVoice() {
        if (this.voices.length === 0) return;

        // Prefer female voices (generally better for children)
        // Look for common kid-friendly voice names
        const preferredVoices = [
            'Karen',           // Often child-friendly
            'Samantha',        // Clear pronunciation
            'Victoria',        // Pleasant tone
            'Allison',         // Good for education
            'Google US English Female',
            'Microsoft Zira',
            'Alex'             // macOS default (good quality)
        ];

        // Try to find preferred voices
        for (const preferred of preferredVoices) {
            const voice = this.voices.find(v =>
                v.name.includes(preferred) && v.lang.startsWith('en')
            );
            if (voice) {
                this.selectedVoice = voice;
                console.log('🎵 Selected voice:', voice.name);
                return;
            }
        }

        // Fallback: prefer female English voices
        const femaleVoice = this.voices.find(v =>
            v.lang.startsWith('en') &&
            (v.name.toLowerCase().includes('female') || v.gender === 'female')
        );

        if (femaleVoice) {
            this.selectedVoice = femaleVoice;
            console.log('🎵 Selected female voice:', femaleVoice.name);
            return;
        }

        // Final fallback: first English voice
        const englishVoice = this.voices.find(v => v.lang.startsWith('en'));
        this.selectedVoice = englishVoice || this.voices[0];
        console.log('🎵 Selected fallback voice:', this.selectedVoice?.name);
    }

    /**
     * Play phonetic sound for a letter
     */
    playLetterSound(letter) {
        if (!this.config.enabled || !this.isSupported) return;

        const lowerLetter = letter.toLowerCase();
        const phoneticSound = PHONETIC_SOUNDS[lowerLetter];

        if (phoneticSound) {
            const text = `${letter.toUpperCase()} says ${phoneticSound}`;
            this.speak(text);
            console.log('🎵 Playing letter sound:', text);
        }
    }

    /**
     * Play number sound
     */
    playNumberSound(number) {
        if (!this.config.enabled || !this.isSupported) return;

        const numberWord = NUMBER_SOUNDS[number];
        if (numberWord) {
            const text = `${number} is ${numberWord}`;
            this.speak(text);
            console.log('🎵 Playing number sound:', text);
        }
    }

    /**
     * Play function key sound
     */
    playFunctionKeySound(key) {
        if (!this.config.enabled || !this.isSupported) return;

        const keyDescription = FUNCTION_KEY_SOUNDS[key.toLowerCase()];
        if (keyDescription) {
            this.speak(keyDescription);
            console.log('🎵 Playing function key sound:', keyDescription);
        }
    }

    /**
     * Main method to play sound for any key
     */
    playKeySound(key) {
        if (!this.config.enabled || !this.isSupported) return;

        const lowerKey = key.toLowerCase();

        // Check if it's a letter
        if (lowerKey.length === 1 && /[a-z]/.test(lowerKey)) {
            this.playLetterSound(lowerKey);
        }
        // Check if it's a number
        else if (lowerKey.length === 1 && /[0-9]/.test(lowerKey)) {
            this.playNumberSound(lowerKey);
        }
        // Check if it's a function key
        else if (FUNCTION_KEY_SOUNDS[lowerKey]) {
            this.playFunctionKeySound(lowerKey);
        }
        else {
            console.log('🔇 No audio defined for key:', key);
        }
    }

    /**
     * Core speech synthesis method
     */
    speak(text) {
        if (!this.isSupported || !text) return;

        // Cancel any ongoing speech
        speechSynthesis.cancel();

        const utterance = new SpeechSynthesisUtterance(text);

        // Apply configuration
        utterance.rate = this.config.rate;
        utterance.pitch = this.config.pitch;
        utterance.volume = this.config.volume;
        utterance.lang = this.config.language;

        // Use selected voice if available
        if (this.selectedVoice) {
            utterance.voice = this.selectedVoice;
        }

        // Error handling
        utterance.onerror = (event) => {
            console.error('🔇 Speech synthesis error:', event.error);
        };

        // Speak the text
        speechSynthesis.speak(utterance);
    }

    /**
     * Toggle audio on/off
     */
    toggle() {
        this.config.enabled = !this.config.enabled;
        this.saveSettings();
        console.log('🎵 Audio', this.config.enabled ? 'enabled' : 'disabled');
        return this.config.enabled;
    }

    /**
     * Enable audio
     */
    enable() {
        this.config.enabled = true;
        this.saveSettings();
        console.log('🎵 Audio enabled');
    }

    /**
     * Disable audio
     */
    disable() {
        this.config.enabled = false;
        speechSynthesis.cancel(); // Stop any ongoing speech
        this.saveSettings();
        console.log('🔇 Audio disabled');
    }

    /**
     * Set speech rate (0.5 to 2.0)
     */
    setRate(rate) {
        this.config.rate = Math.max(0.5, Math.min(2.0, rate));
        this.saveSettings();
        console.log('🎵 Speech rate set to:', this.config.rate);
    }

    /**
     * Set speech pitch (0.5 to 2.0)
     */
    setPitch(pitch) {
        this.config.pitch = Math.max(0.5, Math.min(2.0, pitch));
        this.saveSettings();
        console.log('🎵 Speech pitch set to:', this.config.pitch);
    }

    /**
     * Set volume (0.0 to 1.0)
     */
    setVolume(volume) {
        this.config.volume = Math.max(0.0, Math.min(1.0, volume));
        this.saveSettings();
        console.log('🎵 Volume set to:', this.config.volume);
    }

    /**
     * Get available voices
     */
    getVoices() {
        return this.voices;
    }

    /**
     * Set voice by name or voice object
     */
    setVoice(voice) {
        if (typeof voice === 'string') {
            const foundVoice = this.voices.find(v => v.name === voice);
            if (foundVoice) {
                this.selectedVoice = foundVoice;
                this.saveSettings();
                console.log('🎵 Voice set to:', foundVoice.name);
            }
        } else if (voice && voice.name) {
            this.selectedVoice = voice;
            this.saveSettings();
            console.log('🎵 Voice set to:', voice.name);
        }
    }

    /**
     * Get current configuration
     */
    getConfig() {
        return { ...this.config };
    }

    /**
     * Test audio with a sample phrase
     */
    test() {
        if (!this.isSupported) {
            console.warn('🔇 Audio not supported');
            return;
        }

        this.speak('Hello! This is the Kids Keyboard audio system.');
        console.log('🎵 Playing audio test');
    }

    /**
     * Save settings to localStorage
     */
    saveSettings() {
        if (typeof localStorage === 'undefined') return;

        const settings = {
            enabled: this.config.enabled,
            rate: this.config.rate,
            pitch: this.config.pitch,
            volume: this.config.volume,
            voiceName: this.selectedVoice?.name || null
        };

        localStorage.setItem('kids-keyboard-audio', JSON.stringify(settings));
    }

    /**
     * Load settings from localStorage
     */
    loadSettings() {
        if (typeof localStorage === 'undefined') return;

        try {
            const saved = localStorage.getItem('kids-keyboard-audio');
            if (saved) {
                const settings = JSON.parse(saved);
                this.config.enabled = settings.enabled ?? this.config.enabled;
                this.config.rate = settings.rate ?? this.config.rate;
                this.config.pitch = settings.pitch ?? this.config.pitch;
                this.config.volume = settings.volume ?? this.config.volume;

                // Voice will be set after voices are loaded
                this.savedVoiceName = settings.voiceName;

                console.log('🎵 Audio settings loaded from localStorage');
            }
        } catch (error) {
            console.warn('🔇 Failed to load audio settings:', error);
        }
    }

    /**
     * Get system status
     */
    getStatus() {
        return {
            supported: this.isSupported,
            initialized: this.isInitialized,
            enabled: this.config.enabled,
            voicesAvailable: this.voices.length,
            selectedVoice: this.selectedVoice?.name || 'None'
        };
    }
}

/**
 * Factory function to create audio system
 * @param {Object} options - Configuration options
 * @returns {KidsKeyboardAudio} Audio system instance
 */
function createKidsKeyboardAudio(options = {}) {
    return new KidsKeyboardAudio(options);
}

// Export for different module systems
if (typeof module !== 'undefined' && module.exports) {
    // CommonJS
    module.exports = { KidsKeyboardAudio, createKidsKeyboardAudio };
} else if (typeof window !== 'undefined') {
    // Browser global
    window.KidsKeyboardAudio = KidsKeyboardAudio;
    window.createKidsKeyboardAudio = createKidsKeyboardAudio;
}