/**
 * Kids Keyboard - Main CSS Entry Point
 *
 * Modular CSS architecture for maintainable development.
 * Import core keyboard styles and layout components.
 *
 * @version 0.9.0
 * <AUTHOR>
 * @license MIT
 *
 * ARCHITECTURE:
 * - kids-keyboard-core.css: Stable keyboard and key styles
 * - kids-keyboard-layout.css: Layout containers and responsive design
 * - Individual example CSS files for specific demos
 */

/* Import core keyboard styles (stable) */
@import url('./kids-keyboard-core.css');

/* Import layout and container styles */
@import url('./kids-keyboard-layout.css');

