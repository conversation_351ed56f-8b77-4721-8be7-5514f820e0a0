/**
 * Kids Keyboard - CSS Styles
 * 
 * Lightweight, accessible virtual keyboard styles designed specifically 
 * for children's typing education.
 * 
 * @version 0.9.0
 * <AUTHOR>
 * @license MIT
 * 
 * ACKNOWLEDGMENTS:
 * Design inspired by simple-keyboard (https://github.com/hodgef/simple-keyboard)
 * Optimized for educational use with enhanced accessibility and visual feedback.
 * 
 * FEATURES:
 * - Responsive design (desktop/laptop/tablet)
 * - High contrast colors for young learners
 * - Smooth animations and transitions
 * - Tutor mode visual indicators
 * - Accessibility-focused design
 * - Performance-optimized CSS classes
 */

/* =============================================================================
   KEYBOARD CONTAINER
   ============================================================================= */

.kids-keyboard {
  -moz-user-select: none;
  -webkit-user-select: none;
  background-color: #f5f5f5;
  border: 1px solid black;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  margin: 0 auto;
  max-width: 800px;
  min-width: 320px;
  padding: 12px;
  position: relative;
  user-select: none;
}

/* =============================================================================
   KEYBOARD ROWS
   ============================================================================= */

.kids-keyboard__row {
  display: flex;
  gap: 4px;
  justify-content: center;
  margin-bottom: 6px;
}

.kids-keyboard__row:last-child {
  margin-bottom: 0;
}

/* =============================================================================
   KEYBOARD KEYS - BASE STYLES
   ============================================================================= */

.kids-keyboard__key {
  align-items: center;
  background: white;
  border: 2px solid #ddd;
  border-radius: 6px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  display: flex;
  font-size: 16px;
  font-weight: 500;
  justify-content: center;
  min-height: 45px;
  min-width: 45px;
  outline: none;
  position: relative;
  transition: all 0.15s ease;
}

/* Hover and Focus States */
.kids-keyboard__key:hover,
.kids-keyboard__key:focus {
  border-color: #bbb;
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
  transform: translateY(-1px);
}

.kids-keyboard__key:focus {
  outline: 2px solid #2196F3;
  outline-offset: 2px;
}

.kids-keyboard__key:active {
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  transform: translateY(0);
}

/* =============================================================================
   KEY TYPES - STYLING BY FUNCTION
   ============================================================================= */

/* Normal character keys (letters, numbers, symbols) */
.kids-keyboard__key--normal {
  flex: 1;
}

/* Function keys (Backspace, Enter, Tab, etc.) */
.kids-keyboard__key--function {
  background: #f8f9fa;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Modifier keys (Shift, Caps Lock, etc.) */
.kids-keyboard__key--modifier {
  background: #fff3cd;
  border-color: #ffeaa7;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* Space bar */
.kids-keyboard__key--space {
  flex: 6;
  font-size: 12px;
  font-weight: 600;
  letter-spacing: 0.5px;
  text-transform: uppercase;
}

/* =============================================================================
   SPECIFIC KEY SIZING
   ============================================================================= */

.kids-keyboard__key[data-key="backspace"] {
  flex: 2.5;
}

.kids-keyboard__key[data-key="tab"] {
  flex: 1.5;
}

.kids-keyboard__key[data-key="capslock"] {
  flex: 2.0;
}

.kids-keyboard__key[data-key="enter"] {
  flex: 2.2;
}

.kids-keyboard__key[data-key="shiftleft"],
.kids-keyboard__key[data-key="shiftright"] {
  flex: 2.5;
}

/* =============================================================================
   HIGHLIGHTING STATES - PHYSICAL KEYBOARD SYNC
   ============================================================================= */

.kids-keyboard__key--highlighted {
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  font-weight: 700;
  transform: translateY(-2px);
}

/* Normal keys when highlighted (green) */
.kids-keyboard__key--highlight-normal {
  background-color: #4CAF50 !important;
  border-color: #45a049;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  color: white;
  font-weight: 700;
  transform: translateY(-2px);
}

/* Modifier keys when highlighted (yellow) */
.kids-keyboard__key--highlight-modifier {
  background-color: #FFC107 !important;
  border-color: #ffb300;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  color: #333;
  font-weight: 700;
  transform: translateY(-2px);
}

/* Function keys when highlighted (blue) */
.kids-keyboard__key--highlight-function {
  background-color: #2196F3 !important;
  border-color: #1976D2;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  color: white;
  font-weight: 700;
  transform: translateY(-2px);
}

/* =============================================================================
   ACTIVE MODIFIER STATES
   ============================================================================= */

.kids-keyboard__key--active-modifier {
  background-color: #ff9800;
  border-color: #f57c00;
  box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.2);
  color: white;
}

.kids-keyboard__key--active-modifier.kids-keyboard__key--highlighted {
  background-color: #ff6f00 !important;
  border-color: #e65100;
}

/* =============================================================================
   LAYOUT SWITCHING - DUAL CHARACTER DISPLAY
   ============================================================================= */

/* Default character display */
.kids-keyboard__key .kids-keyboard__key-char--default {
  display: block;
}

.kids-keyboard__key .kids-keyboard__key-char--shift {
  display: none;
}

/* Shift layout - show shifted characters */
.kids-keyboard--shift-layout .kids-keyboard__key .kids-keyboard__key-char--default {
  display: none;
}

.kids-keyboard--shift-layout .kids-keyboard__key .kids-keyboard__key-char--shift {
  display: block;
}

/* =============================================================================
   TUTOR MODE CONTAINER STYLING
   ============================================================================= */

/* Base tutor container - ID because there should only be one per page */
#kids-keyboard-tutor-container {
  background-color: lightpink;
  border: 2px solid fuchsia;
  border-radius: 12px;
  margin: 15px 0;
  padding: 15px;
  position: relative;
  transition: all 0.3s ease;
}

/* Tutor Mode OFF state indicator */
#kids-keyboard-tutor-container::before {
  background: #9e9e9e;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  color: white;
  content: "TUTOR MODE OFF";
  font-size: 12px;
  font-weight: 700;
  left: 50px; /* Make room for emoji */
  letter-spacing: 0.5px;
  padding: 6px 16px;
  position: absolute;
  top: -18px;
  transition: all 0.3s ease;
  z-index: 1000;
}

/* Large emoji indicator */
#kids-keyboard-tutor-container::after {
  content: "🎯";
  font-size: 20px;
  left: 20px;
  position: absolute;
  top: -22px;
  transition: all 0.3s ease;
  z-index: 1001;
}

/* Tutor Mode ON state */
#kids-keyboard-tutor-container.kids-keyboard__tutor--active {
  background-color: rgba(76, 175, 80, 0.08);
  border: 2px solid #4CAF50;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.25);
}

#kids-keyboard-tutor-container.kids-keyboard__tutor--active::before {
  background: linear-gradient(135deg, #4CAF50, #66BB6A);
  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);
  color: white;
  content: "TUTOR MODE ON";
  transform: scale(1.05);
}

#kids-keyboard-tutor-container.kids-keyboard__tutor--active::after {
  font-size: 24px;
  transform: scale(1.1) rotate(5deg);
}

/* Keyboard enhancement in tutor mode */
#kids-keyboard-tutor-container .kids-keyboard {
  transition: box-shadow 0.3s ease;
}

#kids-keyboard-tutor-container.kids-keyboard__tutor--active .kids-keyboard {
  box-shadow: 0 8px 25px rgba(76, 175, 80, 0.15);
}

/* =============================================================================
   INPUT FIELD STYLING
   ============================================================================= */

#kids-keyboard-tutor-input {
  background: #f8f9fa;
  border: 2px solid #e0e0e0;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 16px;
  line-height: 1.5;
  margin-bottom: 20px;
  min-height: 120px;
  padding: 15px;
  resize: vertical;
  transition: all 0.3s ease;
  width: calc(100% - 40px);
}

#kids-keyboard-tutor-input:focus {
  background: white;
  border-color: #4CAF50;
  box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.1);
  outline: none;
}

/* =============================================================================
   RESPONSIVE DESIGN - DESKTOP/LAPTOP/TABLET
   ============================================================================= */

/* Tablet and small desktop */
@media (max-width: 1024px) {
  .kids-keyboard {
    max-width: 90vw;
    padding: 10px;
  }

  .kids-keyboard__key {
    font-size: 14px;
    min-height: 40px;
    min-width: 40px;
  }

  .kids-keyboard__key--function,
  .kids-keyboard__key--modifier {
    font-size: 10px;
  }
}

/* Small tablets and large phones */
@media (max-width: 768px) {
  .kids-keyboard {
    max-width: 95vw;
    padding: 8px;
  }

  .kids-keyboard__key {
    font-size: 12px;
    min-height: 35px;
    min-width: 35px;
  }

  .kids-keyboard__key--function,
  .kids-keyboard__key--modifier {
    font-size: 9px;
  }

  .kids-keyboard__row {
    gap: 3px;
    margin-bottom: 4px;
  }
}

/* =============================================================================
   ACCESSIBILITY ENHANCEMENTS
   ============================================================================= */

/* High contrast mode support */
@media (prefers-contrast: high) {
  .kids-keyboard__key {
    border-width: 3px;
  }

  .kids-keyboard__key--highlight-normal {
    background-color: #2E7D32 !important;
  }

  .kids-keyboard__key--highlight-modifier {
    background-color: #F57F17 !important;
  }

  .kids-keyboard__key--highlight-function {
    background-color: #1565C0 !important;
  }
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
  .kids-keyboard__key,
  #kids-keyboard-tutor-container,
  #kids-keyboard-tutor-container::before,
  #kids-keyboard-tutor-container::after {
    transition: none;
  }

  .kids-keyboard__key:hover,
  .kids-keyboard__key:focus,
  .kids-keyboard__key--highlighted {
    transform: none;
  }
}

/* =============================================================================
   PRINT STYLES
   ============================================================================= */

@media print {
  .kids-keyboard {
    border: 1px solid #ccc;
    box-shadow: none;
  }

  .kids-keyboard__key {
    box-shadow: none;
  }
}