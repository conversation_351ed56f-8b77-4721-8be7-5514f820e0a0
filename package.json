{"name": "kids-keyboard", "version": "0.9.0", "description": "A lightweight, accessible virtual keyboard designed specifically for children's typing education. Inspired by npm package 'simple-keyboard'.", "main": "src/kids-keyboard.js", "types": "src/kids-keyboard.d.ts", "files": ["src/", "README.md", "LICENSE"], "scripts": {"test": "echo \"Tests coming in v1.0.0\" && exit 0"}, "keywords": ["virtual-keyboard", "kids", "children", "typing", "education", "accessibility", "learning", "tutor", "keyboard", "input"], "author": "<PERSON> - Acumen Desktop Software Canada Inc.", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/Acumen-Desktop/Kids-Keyboard.git"}, "bugs": {"url": "https://github.com/Acumen-Desktop/Kids-Keyboard/issues"}, "homepage": "https://github.com/Acumen-Desktop/Kids-Keyboard/blob/main/README.md", "engines": {"node": ">=12.0.0"}, "browserslist": ["> 1%", "last 2 versions", "not ie <= 11"], "devDependencies": {}, "peerDependencies": {}, "dependencies": {}, "acknowledgments": {"inspiration": "This library was inspired by simple-keyboard (https://github.com/hodgef/simple-keyboard), a powerful virtual keyboard library. Kids-keyboard is a lightweight, education-focused derivative optimized specifically for children's typing learning applications."}}